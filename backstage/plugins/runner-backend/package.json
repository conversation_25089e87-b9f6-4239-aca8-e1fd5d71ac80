{"name": "@internal/plugin-runner-backend", "version": "0.1.0", "license": "Apache-2.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin", "pluginId": "runner"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-defaults": "^0.11.1", "@backstage/backend-plugin-api": "^1.4.1", "@backstage/catalog-client": "^1.9.1", "@backstage/catalog-model": "^1.7.5", "@backstage/config": "^1.3.3", "@backstage/errors": "^1.2.7", "@backstage/integration": "^1.17.1", "@backstage/plugin-catalog-node": "^1.17.2", "@octokit/rest": "^22.0.0", "express": "^4.17.1", "express-promise-router": "^4.1.0", "tar-fs": "^3.1.0", "yaml": "^2.3.4", "zod": "^3.22.4"}, "devDependencies": {"@backstage/backend-test-utils": "^1.7.0", "@backstage/cli": "^0.33.1", "@types/express": "^4.17.6", "@types/supertest": "^2.0.12", "@types/tar-fs": "^2.0.4", "supertest": "^6.2.4"}, "files": ["dist"]}